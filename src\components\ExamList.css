.exam-list-container {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.exam-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 2px solid #e9ecef;
}

.exam-list-header h2 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.5rem;
  font-weight: 600;
}

.exam-count {
  margin: 0;
  color: #6c757d;
  font-size: 0.9rem;
  background: #e9ecef;
  padding: 4px 12px;
  border-radius: 20px;
}

.exam-list-scroll {
  overflow-x: auto;
  overflow-y: hidden;
  padding-bottom: 10px;
  margin-bottom: 15px;
}

.exam-list-scroll::-webkit-scrollbar {
  height: 8px;
}

.exam-list-scroll::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.exam-list-scroll::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.exam-list-scroll::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.exam-list {
  display: flex;
  gap: 20px;
  padding: 10px 0;
  min-height: 280px;
}

.exam-card {
  flex: 0 0 300px;
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.exam-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border-color: #007bff;
}

.exam-card.selected {
  border-color: #28a745;
  background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
  transform: translateY(-2px);
}

.exam-card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
}

.exam-title {
  margin: 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: #2c3e50;
  line-height: 1.3;
  flex: 1;
  margin-right: 10px;
}

.exam-badge {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 4px 10px;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 500;
  white-space: nowrap;
}

.exam-card-body {
  flex: 1;
  margin-bottom: 20px;
}

.exam-description {
  color: #6c757d;
  font-size: 0.9rem;
  line-height: 1.4;
  margin-bottom: 15px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.exam-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.9rem;
  color: #495057;
}

.info-icon {
  font-size: 1rem;
}

.exam-card-footer {
  margin-top: auto;
}

.start-exam-btn {
  width: 100%;
  padding: 12px;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.exam-card:not(.selected) .start-exam-btn {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  color: white;
}

.exam-card:not(.selected) .start-exam-btn:hover {
  background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
  transform: translateY(-1px);
}

.exam-card.selected .start-exam-btn {
  background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
  color: white;
}

.exam-list-footer {
  text-align: center;
  margin-top: 10px;
}

.scroll-hint {
  color: #6c757d;
  font-size: 0.8rem;
  margin: 0;
  font-style: italic;
}

/* Loading state */
.exam-list-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: #6c757d;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Error state */
.exam-list-error {
  text-align: center;
  padding: 40px;
  color: #dc3545;
}

.retry-button {
  background: #dc3545;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  margin-top: 10px;
  transition: background 0.3s ease;
}

.retry-button:hover {
  background: #c82333;
}

/* Empty state */
.exam-list-empty {
  text-align: center;
  padding: 40px;
  color: #6c757d;
  font-size: 1.1rem;
}

/* Responsive design */
@media (max-width: 768px) {
  .exam-list-container {
    padding: 15px;
  }
  
  .exam-card {
    flex: 0 0 280px;
    padding: 15px;
  }
  
  .exam-list-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .exam-title {
    font-size: 1.1rem;
  }
}

@media (max-width: 480px) {
  .exam-list-container {
    padding: 10px;
  }
  
  .exam-card {
    flex: 0 0 250px;
    padding: 12px;
  }
  
  .exam-list-header h2 {
    font-size: 1.3rem;
  }
  
  .scroll-hint {
    display: none;
  }
}
