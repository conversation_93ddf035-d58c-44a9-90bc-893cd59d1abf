1. MS Winword 2013: so n th o nhanh m t c m t l p l i nhi u l n trong v n b n, ta thi t l p t g t t b ng c ch v o File Optoin Proofing AutoOptoins..., ta ch n ti p c ng c n o sau y? A. AutoFormat B. Math Autocorrect C. Autocorrect D. AutoFormat As You Type Answer: C 2. MS Winword 2013: khi d ng c ng c Autocorrect, ta nh p t g t t r i nh p c m t c thay th b ng t g t t v o c c m c t ng ng n o sau y? A. Replace - Replace With B. Find What - Replace With C. Find What - Replace D. Replace -With Answer: D 3. MS Winword 2013: khi c n thay th m t n i dung c trong v n b n th nh m t n i dung m i, ta nh p n i dung c r i nh p n i dung m i thay th cho n i dung c v o c c m c t ng ng n o sau y? A. Replace - Replace With B. Find What - Replace With C. Find What - Replace D. Replace -With Answer: B 4. MS Winword 2013: L m c ch n o th m m t tr ch d n v o v n b n b ng References trong Microsoft Word? A. Ch n v tr trong v n b n m b n mu n th m tr ch d n. B. Trong tab References, ch n Insert Citation. C. Ch n ki u tr ch d n ph h p (v d : APA, MLA) ho c t o ki u tr ch d n t y ch nh. D. Nh p th ng tin c a ngu n tham kh o (t c gi , ti u , n m xu t b n, vv.) trong h p tho i tr ch d n. Answer: B 7. MS Winword 2013: khi th c hi n ng t trang sang trang m i, ph ng n n o sau y l ng? A. D ng ph m Enter B. D ng t h p ph m Alt Enter C. D ng t h p ph m Ctrl Enter D. D ng t h p ph m Shift Enter Answer: C 8. MS Winword 2013: khi th c hi n ng t trang sang trang m i, ph ng n n o sau y l ng? A. D ng ph m Enter B. D ng t h p ph m Alt Enter C. D ng l nh Insert Page Break D. D ng l nh Page Layout Break Next Page Answer: C 9. MS Winword 2013: c ng c format painter c ch c n ng g ? A. Sao ch p m t nh d ng c trong v n b n B. T o m t nh d ng m i cho v n b n C. L c ng c nh d ng cho c c h nh v D. L c ng c v h nh Answer: A 10. MS Winword 2013: khi th c hi n ng t o n sang o n m i, ph ng n n o sau y l ng? A. D ng l nh Insert Page Break B. D ng l nh Page Layout Break Next Page C. D ng t h p ph m Ctrl Enter D. D ng t h p ph m Shift Enter Answer: B 11. MS Winword 2013: v n b n c nhi u ch ng, mu n nh s trang b t u t 1 cho t ng ch ng, ta ph i th c hi n thao t c n o sau y? A. Ng t o n cu i m i ch ng B. D ng ph m Enter sang trang m i cu i m i ch ng C. Ng t trang cu i m i ch ng D. D ng t h p ph m Shift Enter Answer: A 12. MS Winword 2013: chia c t cho v n b n ta ph i ch n o n v n b n c n chia c t r i th c hi n c m thao t c n o sau y? A. Insert Columns More Columns B. Layout Columns More Columns C. Insert Columns D. Page Layout Columns More Columns Answer: D 13. MS Winword 2013: t o B ng cho vi c t ch c d li u d ng danh s ch ta th c hi n c m thao t c n o sau y? A. Page Layout Insert Table B. References Table of Contents C. References Insert Table D. Insert Table Insert Table Answer: D 14. MS Winword 2013: khi s d ng B ng mu n l p l i d ng u ti n c a b ng tr n nhi u trang, ta ch n d ng u ti n c a B ng v th c hi n c m thao t c n o sau y? A. Insert Header B. Design Header Row C. Layout Repeat Header Rows D. Design First Row Answer: C 15. MS Winword 2013: khi mu n tr n c c li n ti p trong B ng l i th nh 1 , ta ch n c c c n tr n v th c hi n c m thao t c n o sau y? A. Layout Merge cells B. Insert Merge cells C. Home Font Merge cells D. Maillings Start Mail Merge Answer: A 16. MS Winword 2013: khi mu n tr n c c li n ti p trong B ng l i th nh 1 , ta ch n c c c n tr n v th c hi n c m thao t c n o sau y? A. Right Click Merge cells B. Insert Merge cells C. Home Font Merge cells D. Maillings Start Mail Merge Answer: A 17. MS Winword 2013: khi mu n chia 1 c tr n t nhi u kh c trong B ng th nh nhi u , ta ch n c n chia v th c hi n c m thao t c n o sau y? A. Right Click Insert Insert cells B. Insert Cells C. Page Layout Line Numbers D. Right Click Split cells Answer: D 18. MS Winword 2013: khi mu n chia 1 c tr n t nhi u kh c trong B ng th nh nhi u , ta ch n c n chia v th c hi n c m thao t c n o sau y? A. Right Click Insert Insert cells B. Layout Split cells C. Page Layout Line Numbers D. Insert Cells Answer: B 19. MS Winword 2013: nh d ng h ng c a v n b n trong m t c a B ng ta ch n c n nh d ng v th c hi n c m thao t c n o sau y? A. Right Click Cells Alignment B. Right Click Text Directoin C. Home Font UnderLine Style D. View Gridlines Answer: B 20. MS Winword 2013: nh d ng h ng c a v n b n trong m t c a B ng ta ch n c n nh d ng v th c hi n c m thao t c n o sau y? A. Layout Cells Alignment B. Home Font UnderLine Style C. Layout Text Directoin D. View Gridlines Answer: C 21. MS Winword 2013: t o ti u chung ch n trang cho c c trang v n b n ta th c hi n c m thao t c n o sau y? A. Insert Footer B. References Insert Footnote C. References Insert Endnote D. Insert Endnote Answer: A 22. MS Winword 2013: t o ch th ch cu i trang cho m t n i dung n o trong trang v n b n, ta th c hi n c m thao t c n o sau y? A. Insert Footer B. References Insert Footnote C. References Insert Endnote D. Insert Footnote Answer: B 23. MS Winword 2013: t o ch th ch cho m t n i dung n o trong v n b n v ph n ch th ch c hi n th cu i v n b n, ta th c hi n c m thao t c n o sau y? A. Insert Footer B. References Insert Footnote C. References Insert Endnote D. Insert Footnote Answer: C 26. MS Winword 2013: khi th c hi n thao t c Insert Picture l ch n v o v n b n: A. M t h nh nh c ng i d ng l u tr n a B. M t h nh nh l u trong th vi n h nh nh c a Word C. M t m u ch ngh thu t D. M t bi u ( th ) Answer: A 27. MS Winword 2013: khi th c hi n thao t c Insert Chart l ch n v o v n b n: A. M t h nh nh c ng i d ng l u tr n a B. M t h nh nh l u trong th vi n h nh nh c a Word C. M t m u ch ngh thu t D. M t bi u ( th ) Answer: D 28. MS Winword 2013: khi th c hi n thao t c Insert Clip Art l ch n v o v n b n: A. M t h nh nh c ng i d ng l u tr n a B. M t h nh nh l u trong th vi n h nh nh c a Word C. M t m u ch ngh thu t D. M t bi u ( th ) Answer: B 29. MS Winword 2013: khi th c hi n thao t c Insert WordArt l ch n v o v n b n: A. M t h nh nh c ng i d ng l u tr n a B. M t h nh nh l u trong th vi n h nh nh c a Word C. M t m u ch ngh thu t D. M t bi u ( th ) Answer: C 30. MS Winword 2013: t o m c l c t ng cho v n b n, sau khi thi t l p Level cho c c n i dung t o m c l c, ta t con tr t i v tr t o m c l c v th c hi n c m thao t c n o sau y? A. References Table of Contents Insert Table of Contents B. Insert Table C. References Insert Table of Figure D. References Insert Index Answer: A 31. MS Winword 2013: khi th c hi n tr n th ta th c hi n thao t c Mailling Select Recipients Use Existing List..., h th ng s y u c u ta l m g ti p theo? A. Ch ng d n n File d li u ngu n cung c p cho vi c tr n th . B. T o m i m t danh s ch d li u cung c p cho vi c tr n th C. Ch nh tr ng d li u ch n v o t i li u g c D. Xem k t qu tr n th Answer: A 32. MS Winword 2013: khi th c hi n tr n th ta th c hi n thao t c Mailling Select Recipients Type New List..., h th ng s y u c u ta l m g ti p theo? A. Ch ng d n n File d li u ngu n cung c p cho vi c tr n th . B. T o m i m t danh s ch d li u cung c p cho vi c tr n th C. Ch nh tr ng d li u ch n v o t i li u g c D. Xem k t qu tr n th Answer: B 33. MS Winword 2013: khi th c hi n tr n th ta th c hi n thao t c Mailling Insert Merge Field, h th ng s y u c u ta l m g ti p theo? A. Ch ng d n n File d li u ngu n cung c p cho vi c tr n th . B. T o m i m t danh s ch d li u cung c p cho vi c tr n th C. Ch nh tr ng d li u ch n v o t i li u g c D. Xem k t qu tr n th Answer: C 37. MS Winword 2013: khi v n b n c g n m t kh u, l m vi c tr n v n b n n y, ng i d ng ph i nh p m t kh u khi n o? A. Khi th c hi n ch nh s a th ng tin tr n v n b n B. Khi L u v n b n v i n i dung c ch nh s a C. Khi l u v n b n v i m t t n m i D. Khi m v n b n Answer: D 40. MS Winword 2013: t i c a s Page Setup ta ch n Port Trait v i ch ch g ? A. Ch nh in trang d c B. Ch n kh gi y A4 C. Ch nh in trang ngang D. Ch n kh gi y A5 Answer: A 41. MS Winword 2013: t i c a s Page Setup ta ch n LandScape v i ch ch g ? A. Ch nh in trang d c B. Ch n kh gi y A4 C. Ch nh in trang ngang D. Ch n kh gi y A5 Answer: C 50. MS Winword 2013: khi mu n in trang 5, con tr so n th o ang trang 5 trong v n b n c 20 trang, ta thao t c File Print, ti p theo ta ch nh trang in theo c ch n o sau y? A. Print Current Page B. T i m c Pages g v o 1-5 C. T i m c Pages g v o 1 D. T i m c Pages g v o 1-1,5 Answer: A 51. MS Winword 2013: Ch n o kh ng thu c Menu Home A. Links B. Font C. Paragraph D. Styles Answer: A 52. MS Winword 2013: Ch n o kh ng thu c Menu Home A. Tables B. Font C. Paragraph D. Styles Answer: A 53. MS Winword 2013: Ch n o kh ng thu c Menu Home A. Header Footer B. Font C. Paragraph D. Styles Answer: A 54. MS Winword 2013: Ch n o kh ng thu c Menu Home A. Text B. Font C. Paragraph D. Styles Answer: A 55. MS Winword 2013: Ch n o kh ng thu c Menu Insert A. Font B. Links C. Tables D. Header Footer Answer: A 56. MS Winword 2013: Ch n o kh ng thu c Menu Insert A. Paragraph B. Links C. Tables D. Header Footer Answer: A 57. MS Winword 2013: Ch n o kh ng thu c Menu Insert A. Styles B. Links C. Tables D. Header Footer Answer: A 58. MS Winword 2013: Ch n o kh ng thu c Menu Insert A. Clipboard B. Links C. Tables D. Header Footer Answer: A 59. MS Winword 2013: Ch n o kh ng thu c Menu Insert A. Editing B. Links C. Tables D. Header Footer Answer: A 60. MS Winword 2013: Ch n o thu c Menu Page Layout A. Themes B. Links C. Font D. Styles Answer: A 61. MS Winword 2013: Ch n o thu c Menu Page Layout A. Page Setup B. Links C. Font D. Styles Answer: A 62. MS Winword 2013: Ch n o thu c Menu Page Layout A. Paragraph B. Links C. Font D. Styles Answer: A 63. MS Winword 2013: Ch n o thu c Menu Page Layout A. Page Backgroud B. Links C. Font D. Styles Answer: A 64. MS Winword 2013: Ch n o thu c Menu Page Layout A. Arrange B. Links C. Font D. Styles Answer: A 65. MS Winword 2013: Ch n o thu c Menu Insert A. Links B. Page Setup C. Page Backgroud D. Paragraph Answer: A 66. MS Winword 2013: Ch n o thu c Menu Insert A. Tables B. Page Setup C. Page Backgroud D. Paragraph Answer: A 67. MS Winword 2013: Ch n o thu c Menu Insert A. Header Footer B. Page Setup C. Page Backgroud D. Paragraph Answer: A 68. MS Winword 2013: Ch n o thu c Menu Insert A. Pages B. Page Setup C. Page Backgroud D. Paragraph Answer: A 69. MS Winword 2013: Ch n o thu c Menu Insert A. Symbols B. Page Setup C. Page Backgroud D. Paragraph Answer: A 70. MS Winword 2013: Ch n o thu c Menu Home A. Font B. Links C. Themes D. Symbols Answer: A 71. MS Winword 2013: Ch n o thu c Menu Home A. Paragraph B. Links C. Themes D. Symbols Answer: A 72. MS Winword 2013: Ch n o thu c Menu Home A. Styles B. Links C. Themes D. Symbols Answer: A 73. MS Winword 2013: Ch n o thu c Menu Home A. Editing B. Links C. Themes D. Symbols Answer: A 74. MS Winword 2013: Ch n o thu c Menu References A. Table of Contents B. Page Setup C. Tables D. Paragraph Answer: A 75. MS Winword 2013: Ch n o thu c Menu References A. Footnotes B. Page Setup C. Tables D. Paragraph Answer: A 76. MS Winword 2013: Ch n o thu c Menu References A. Captions B. Page Setup C. Tables D. Paragraph Answer: A 77. MS Winword 2013: Ch n o thu c Menu References A. Index B. Page Setup C. Tables D. Paragraph Answer: A 78. MS Winword 2013: Ch n o thu c Menu References A. Table of Authorities B. Page Setup C. Tables D. Paragraph Answer: A 79. MS Winword 2013: Ch n o kh ng thu c Menu References A. Themes B. Index C. Footnotes D. Table of Contents Answer: A 80. MS Winword 2013: Ch n o kh ng thu c Menu References A. Page Setup B. Index C. Footnotes D. Table of Contents Answer: A 81. MS Winword 2013: Ch n o kh ng thu c Menu References A. Paragraph B. Index C. Footnotes D. Table of Contents Answer: A 82. MS Winword 2013: Ch n o kh ng thu c Menu References A. Page Backgroud B. Index C. Footnotes D. Table of Contents Answer: A 83. MS Winword 2013: Ch n o kh ng thu c Menu References A. Arrange B. Index C. Footnotes D. Table of Contents Answer: A 84. MS Winword 2013: Ch n o kh ng thu c Menu References A. Links B. Index C. Footnotes D. Table of Contents Answer: A 85. MS Winword 2013: Ch n o kh ng thu c Menu References A. Tables B. Index C. Footnotes D. Table of Contents Answer: A 86. MS Winword 2013: Ch n o kh ng thu c Menu References A. Header Footer B. Index C. Footnotes D. Table of Contents Answer: A 87. MS Winword 2013: Ch n o kh ng thu c Menu References A. Pages B. Index C. Footnotes D. Table of Contents Answer: A 88. MS Winword 2013: Ch n o kh ng thu c Menu References A. Symbols B. Index C. Footnotes D. Table of Contents Answer: A 89. MS Winword 2013: Ch n o kh ng thu c Menu References A. Font B. Index C. Footnotes D. Table of Contents Answer: A 90. MS Winword 2013: Ch n o kh ng thu c Menu References A. Paragraph B. Index C. Footnotes D. Table of Contents Answer: A 91. MS Winword 2013: Ch n o kh ng thu c Menu References A. Styles B. Index C. Footnotes D. Table of Contents Answer: A 92. MS Winword 2013: Ch n o kh ng thu c Menu References A. Editing B. Index C. Footnotes D. Table of Contents Answer: A 93. MS Winword 2013: L nh n o ch n danh s ch khi th c hi n Mailings A. Start Mail Merge B. Finish C. Preview Results D. Insert Merge Field Answer: A 94. MS Winword 2013: L nh n o ch n th ng tin khi th c hi n Mailings A. Insert Merge Field B. Finish C. Preview Results D. Start Mail Merge Answer: A 95. MS Winword 2013: L nh n o xem tr c tr n th khi th c hi n Mailings A. Preview Results B. Start Mail Merge C. Finish D. Insert Merge Field Answer: A 96. MS Winword 2013: L nh n o th c hi n tr n th khi th c hi n Mailings A. Finish B. Start Mail Merge C. Preview Results D. Insert Merge Field Answer: A 97. MS Winword 2013: L nh n o kh ng thu c c ng vi c Mailings A. Footnotes B. Start Mail Merge C. Preview Results D. Insert Merge Field Answer: A 98. MS Winword 2013: L nh n o kh ng thu c c ng vi c Mailings A. Index B. Start Mail Merge C. Preview Results D. Insert Merge Field Answer: A 99. MS Winword 2013: L nh n o kh ng thu c c ng vi c Mailings A. Links B. Start Mail Merge C. Preview Results D. Insert Merge Field Answer: A 100. MS Winword 2013: L nh n o kh ng thu c c ng vi c Mailings A. Footnotes B. Start Mail Merge C. Preview Results D. Insert Merge Field Answer: A 101. MS Winword 2013: L nh n o kh ng thu c c ng vi c Mailings A. Captions B. Start Mail Merge C. Preview Results D. Insert Merge Field Answer: A 102. MS Winword 2013: L nh n o kh ng thu c c ng vi c Mailings A. Editing B. Start Mail Merge C. Preview Results D. Insert Merge Field Answer: A 103. MS Winword 2013: L nh n o kh ng thu c c ng vi c Mailings A. Insert Merge Field B. Arrange C. Start Mail Merge D. Preview Results Answer: B 104. MS Winword 2013: L nh n o kh ng thu c c ng vi c Mailings A. Themes B. Start Mail Merge C. Preview Results D. Insert Merge Field Answer: A 105. MS Winword 2013: Ch nh d ng n o sau y d ng th t u d ng o n v n b n A. Indent B. Paragraph: Tab C. Numberring D. Column Answer: A 106. MS Winword 2013: Ch nh d ng n o sau y d ng th t d ng con o n v n b n A. Indent B. Paragraph: Tab C. Numberring D. Column Answer: A 107. MS Winword 2013: Ch nh d ng n o sau y d ng canh t gi a v n b n A. Paragraph B. Dropcap C. Numberring D. Column Answer: A 108. MS Winword 2013: Ch nh d ng n o sau y d ng canh t b n tr i v n b n A. Paragraph B. Dropcap C. Numberring D. Column Answer: A 109. MS Winword 2013: Ch nh d ng n o sau y d ng canh t b n ph i v n b n A. Paragraph B. Dropcap C. Numberring D. Column Answer: A 110. MS Winword 2013: Ch nh d ng n o sau y d ng canh u v n b n A. Paragraph B. Dropcap C. Numberring D. Column Answer: A 111. MS Winword 2013: Ch nh d ng n o sau y d ng ch n m u ch c a t A. Font B. Indent C. Numberring D. Column Answer: A 112. MS Winword 2013: Ch nh d ng n o sau y d ng ch n m u n n c a t A. Font B. Indent C. Numberring D. Column Answer: A 113. MS Winword 2013: Ch nh d ng n o sau y d ng l m nghi ng t A. Font B. Indent C. Numberring D. Column Answer: A 114. MS Winword 2013: Ch nh d ng n o sau y d ng l m m t A. Font B. Indent C. Numberring D. Column Answer: A 115. MS Winword 2013: Ch nh d ng n o sau y d ng g ch ch n t A. Font B. Indent C. Numberring D. Column Answer: A 116. MS Winword 2013: Ch nh d ng n o sau y d ng canh gi a t v tr b t k A. Tab B. Dropcap C. Numberring D. Column Answer: A 117. MS Winword 2013: Ch nh d ng n o sau y d ng canh tr i t v tr b t k A. Tab B. Dropcap C. Numberring D. Column Answer: A 118. MS Winword 2013: Ch nh d ng n o sau y d ng canh ph i t v tr b t k A. Tab B. Dropcap C. Numberring D. Column Answer: A 119. MS Winword 2013: Ch nh d ng n o sau y d ng c ch d ng tr n c a d ng ang ch n A. Paragraph - Spacing B. Paragraph - Tab C. Paragraph - Leader D. Paragraph - Font Answer: A 120. MS Winword 2013: Ch nh d ng n o sau y d ng c ch d ng d i c a d ng ang ch n A. Paragraph - Spacing B. Paragraph - Tab C. Paragraph - Leader D. Paragraph - Font Answer: A 121. MS Winword 2013: Ch nh d ng n o sau y d ng t o c c d u ch m t ng A. Paragraph - Tab B. Paragraph - Spacing C. Paragraph - Leader D. Paragraph - Font Answer: A 122. MS Winword 2013: Ch nh d ng n o sau y d ng t o ch m c 3.1.1 A. Numberring B. Indent C. Font D. Column Answer: A 123. MS Winword 2013: Ch nh d ng n o sau y d ng chia c c o n v n th nh 2 c t A. Column B. Indent C. Font D. Numberring Answer: A 124. MS Winword 2013: Ch nh d ng n o sau y d ng chia c c o n v n th nh 4 c t A. Column B. Indent C. Font D. Numberring Answer: A 125. MS Winword 2013: K thu t n o d ng g p c c c a Table A. Merge cell B. Auto Fix C. Cell Alignment D. Border Shadding Answer: A 126. MS Winword 2013: K thu t n o d ng ch nh r ng c a Table A. Auto Fix B. Merge cell C. Cell Alignment D. Border Shadding Answer: A 127. MS Winword 2013: K thu t n o d ng canh ch n m gi a trong c a Table A. Cell Alignment B. Auto Fix C. Merge cell D. Border Shadding Answer: A 128. MS Winword 2013: K thu t n o d ng k c c ng ngo i c ng c a Table A. Border Shadding B. Auto Fix C. Cell Alignment D. Merge cell Answer: A 129. MS Winword 2013: K thu t n o d ng k ng ch o trong c a Table A. Border Shadding B. Auto Fix C. Cell Alignment D. Merge cell Answer: A 130. MS Winword 2013: K thu t n o d ng t ch d c trong c a Table A. Text Direction B. Auto Fix C. Cell Alignment D. Merge cell Answer: A 131. MS Winword 2013: K thu t n o d ng t m u n n c c d ng c a Table A. Border Shadding B. Auto Fix C. Cell Alignment D. Merge cell Answer: A 132. MS Winword 2013: K thu t n o d ng ch n ki u t n n c c d ng c a Table A. Border Shadding B. Auto Fix C. Cell Alignment D. Merge cell Answer: A 133. MS Winword 2013: K thu t n o c nh b c h nh trong v n b n A. In Line with Text B. In Front of Text C. Tight D. Paragraph - Font Answer: A 134. MS Winword 2013: K thu t n o cho ph p b c h nh i t do trong v n b n A. In Front of Text B. In Line with Text C. Tight D. Paragraph - Font Answer: A 135. MS Winword 2013: K thu t n o t b c h nh l t gi a v n b n A. Tight B. In Front of Text C. In Line with Text D. Paragraph - Font Answer: A 136. MS Winword 2013: K thu t n o t b c h nh l t gi a v n b n A. Square B. In Front of Text C. Tight D. Paragraph - Font Answer: A 137. MS Winword 2013: K thu t n o t b c h nh l t gi a v n b n A. Through B. In Front of Text C. Tight D. Paragraph - Font Answer: A 138. MS Winword 2013: K thu t n o quy nh c ch t h nh nh trong v n b n A. Wrap Text B. Add Text C. Edit Points D. Insert Caption Answer: A 139. MS Winword 2013: K thu t n o ch n ch v o h nh shape A. Add Text B. Wrap Text C. Edit Points D. Insert Caption Answer: A 140. MS Winword 2013: K thu t n o l m thay i v tr c c c nh h nh shape A. Edit Points B. Add Text C. Wrap Text D. Insert Caption Answer: A 141. MS Winword 2013: K thu t n o ch n ti u cho h nh shape A. Insert Caption B. Add Text C. Edit Points D. Wrap Text Answer: A 150. MS Winword 2013: K thu t n o di chuy n number khi nh d ng Numbering A. First Line B. Hanging C. Left Indent D. Right Indent Answer: A 151. MS Winword 2013: k thu t n o di chuy n ti u khi nh d ng Numbering A. Hanging B. First Line C. Left Indent D. Right Indent Answer: A 152. MS Winword 2013: k thu t n o di chuy n ti u khi nh d ng Numbering A. Tab B. First Line C. Left Indent D. Right Indent Answer: A 166. MS Winword 2013: r ng c a Table s d ng k thu t n o A. AutoFix Contents B. AutoFix Windows C. T do D. Paragraph - Font Answer: A 167. MS Winword 2013: Table n m nh ng trong v n b n, ta s d ng k thu t n o A. Paragraph B. Column C. Tab D. Numbering Answer: A 168. MS Winword 2013: t o kho ng h c a c c d ng, c c o n trong v n b n, ta s d ng k thu t n o A. Paragraph B. Ph m Enter C. Font D. Numbering Answer: A 25. MS Excel 2013: g n m t kh u cho Workbook hi n h nh ta th c hi n c m thao t c n o sau y? A. v o File Info Protect Workbook Encrypt With Password B. v o File Info Protect Workbook Protect CurrentSheet C. v o File Info Protect Workbook Protect Workbook Structure D. Click chu t ph i t i t n Sheet Protect Sheet Answer: A 26. MS Excel 2013: g n m t kh u cho Work Sheet hi n h nh ta th c hi n c m thao t c n o sau y? A. V o File Info Protect Workbook Encrypt With Password B. V o File New Info Protect Workbook Protect CurrentSheet C. V o File New Protect CurrentSheet D. V o File Edit Protect CurrentSheet Answer: A 27. MS Excel 2013: g n m t kh u cho Workbook h th ng y u c u ta nh p m t kh u m y l n? A. 1 B. 2 C. 3 D. 1 ho c 2 l n u c Answer: B 28. MS Excel 2013: m m t Workbook c g n m t kh u h th ng y u c u ta nh p m t kh u m y l n? A. 1 B. 2 C. Kh ng y u c u nh p. D. 1 ho c 2 l n u c Answer: A 29. MS Excel 2013: g n m t kh u cho WorkSheet h th ng y u c u ta nh p m t kh u m y l n? A. 1 B. 2 C. 3 D. 1 ho c 2 l n u c Answer: B 30. MS Excel 2013: thao t c tr n WorkSheet c g n m t kh u h th ng y u c u ta nh p m t kh u m y l n? A. 1 B. 2 C. Kh ng y u c u nh p m t kh u v g n m t kh u ch b o v WorkSheet kh ng b x a. D. 1 ho c 2 l n u c Answer: A 31. MS Excel 2013: x a m t kh u cho Workbook hi n h nh ta th c hi n c m thao t c n o sau y? A. v o File Info Protect Workbook Encrypt With Password, x a m t kh u v l u Flie B. v o File Info Protect Workbook Protect CurrentSheet, x a m t kh u v l u Flie C. v o File Info Protect Workbook Protect Workbook Structure D. Click chu t ph i t i t n Sheet Unprotect Sheet Answer: A 32. MS Excel 2013: Trong b ng t nh Excel, khi ta th c hi n l nh Data Filter v ch nh i u ki n l c t i m t Tr ng (c t) tr n c s d li u, Ph t bi u n o sau y l ng? A. C s d li u ch hi n th nh ng d ng th a m n i u ki n l c t i c t c ch nh i u ki n l c B. C s d li u s x a nh ng d ng kh ng th a m n i u ki n l c t i c t c ch nh i u ki n l c C. C s d li u s xu t nh ng d ng th a m n i u ki n l c t i c t c ch nh i u ki n l c ra v tr kh c tr n b ng t nh D. C s d li u kh ng hi n th c c d ng th a m n i u ki n l c Answer: A 33. MS Excel 2013: Trong b ng t nh Excel, khi ta th c hi n l nh Data Filter v ch nh i u ki n l c t i m t Tr ng (c t) tr n c s d li u, Ph t bi u n o sau y l ng? A. C s d li u s xu t nh ng d ng th a m n i u ki n l c t i c t c ch nh i u ki n l c ra v tr kh c tr n b ng t nh B. C s d li u s x a nh ng d ng kh ng th a m n i u ki n l c t i c t c ch nh i u ki n l c C. C s d li u s n nh ng d ng kh ng th a m n i u ki n l c t i c t c ch nh i u ki n l c D. C s d li u s xu t nh ng d ng kh ng c n th a m n i u ki n l c Answer: C 34. MS Excel 2013: Trong b ng t nh Excel, khi ta th c hi n l nh Data Filter v ch nh i u ki n l c t i hai Tr ng (c t) tr n c s d li u, Ph t bi u n o sau y l ng? A. C s d li u s xu t nh ng d ng th a m n i u ki n l c t i c hai c t c ch nh i u ki n l c ra v tr kh c tr n b ng t nh B. C s d li u s n nh ng d ng kh ng th a m n c hai i u ki n l c t i c c c t c ch nh i u ki n l c C. C s d li u s n nh ng d ng kh ng th a m n m t trong hai i u ki n l c t i c c c t c ch nh i u ki n l c D. C s d li u s hi n th nh ng d ng kh ng th a m n Answer: B 35. MS Excel 2013: Trong b ng t nh Excel, khi ta th c hi n l nh Data Filter v ch nh i u ki n l c t i hai Tr ng (c t) tr n c s d li u, Ph t bi u n o sau y l ng? A. C s d li u ch hi n th nh ng d ng th a m n c hai i u ki n l c t i c c c t c ch nh i u ki n l c B. C s d li u ch hi n th nh ng d ng th a m n m t trong hai i u ki n l c t i c c c t c ch nh i u ki n l c C. C s d li u s xu t nh ng d ng th a m n c hai i u ki n l c t i c c c t c ch nh i u ki n l c ra v tr kh c tr n b ng t nh D. C s d li u s xu t nh ng d ng kh ng th a m n c hai i u ki n l c t i c c c t c ch nh i u ki n l c ra v tr kh c tr n b ng t nh Answer: A 36. MS Excel 2013: Trong b ng t nh Excel, khi ta th c hi n tr ch xu t d li u b ng l nh Data Advanced th ph i t o mi n i u ki n, ph t bi u n o sau y v v tr c a mi n i u ki n l ng nh t? A. Ph i t o t i Sheet ch a c s d li u B. Ph i t o t i Sheet ch a k t qu tr ch xu t d li u C. T i Sheet ch a k t qu tr ch xu t d li u ho c Sheet ch a c s d li u D. T i v tr t y ch n m kh ng l m nh h ng n C s d li u Answer: D 37. MS Excel 2013: Trong b ng t nh Excel, khi ta th c hi n tr ch xu t d li u b ng l nh Data Advanced, c 2 i u ki n tr ch xu t li n quan n 2 tr ng d li u v ph i th a m n ng th i, ph ng n nh p i u ki n n o sau y l ng? A. Nh p 2 i u ki n tr n c ng d ng B. Nh p 2 i u ki n tr n kh c d ng kh c c t C. Nh p 2 i u ki n tr n kh c d ng c ng c t D. Nh p 3 i u ki n tr n kh c d ng c ng c t Answer: A 38. MS Excel 2013: Trong b ng t nh Excel, khi ta th c hi n tr ch xu t d li u b ng l nh Data Advanced, c 2 i u ki n tr ch xu t li n quan n 2 tr ng d li u v ch c n th a m n 1 trong 2 i u ki n, ph ng n nh p i u ki n n o sau y l ng? A. Nh p 2 i u ki n tr n c ng d ng B. Nh p 2 i u ki n tr n kh c d ng kh c c t C. Nh p 2 i u ki n tr n kh c d ng c ng c t D. Nh p 3 i u ki n tr n kh c d ng c ng c t Answer: B 39. MS Excel 2013: Trong b ng t nh Excel, khi ta th c hi n tr ch xu t d li u b ng l nh Data Advanced, c 2 i u ki n tr ch xu t li n quan n 1 tr ng d li u v ph i th a m n ng th i, ph ng n nh p i u ki n n o sau y l ng? A. T n tr ng c s d ng 2 l n trong mi n i u ki n, nh p 2 i u ki n tr n c ng d ng B. T n tr ng c s d ng 2 l n trong mi n i u ki n, nh p 2 i u ki n tr n kh c d ng kh c c t C. Nh p 2 i u ki n tr n kh c d ng c ng c t D. Nh p 2 i u ki n tr n c ng d ng c ng c t Answer: A 40. MS Excel 2013: Trong b ng t nh Excel, khi ta th c hi n tr ch xu t d li u b ng l nh Data Advanced, c 2 i u ki n tr ch xu t li n quan n 1 tr ng d li u v ch c n th a m n 1 trong 2 i u ki n, ph ng n nh p i u ki n n o sau y l ng? A. T n tr ng c s d ng 2 l n trong mi n i u ki n, nh p 2 i u ki n tr n c ng d ng B. T n tr ng c s d ng 2 l n trong mi n i u ki n, nh p 2 i u ki n tr n kh c d ng kh c c t C. Nh p 2 i u ki n tr n kh c d ng c ng c t D. Nh p 2 i u ki n tr n c ng d ng c ng c t Answer: C 41. MS Excel 2013: Trong b ng t nh Excel, khi ta th c hi n tr ch xu t d li u b ng l nh Data Advanced v ch nh mi n i u ki n, ph t bi u v k t qu n o sau y l ng? A. C s d li u ch hi n th nh ng d ng th a m n mi n i u ki n v n c c d ng c n l i B. C s d li u s x a nh ng d ng kh ng th a m n mi n i u ki n C. C s d li u s xu t nh ng d ng th a m n mi n i u ki n ra v tr kh c D. Nh p 2 i u ki n tr n c ng d ng c ng c t Answer: C 42. MS Excel 2013: Trong b ng t nh Excel c 2 Sheets (Sheet1 v Sheet2). C s d li u n m tr n Sheet1, mi n i u ki n n m Sheet2, khi ta Sheet1 v th c hi n tr ch xu t d li u b ng l nh Data Advanced k t qu th c hi n cho ph p xu t ra u? A. Sheet1 B. Sheet2 C. Sheet1 ho c Sheet2 u c D. K t qu xu t ra Sheet m i (kh ng ph i Sheet1 hay Sheet2) Answer: A 43. MS Excel 2013: Trong b ng t nh Excel c 2 Sheets (Sheet1 v Sheet2). C s d li u v mi n i u ki n n m tr n Sheet1, khi ta Sheet2 v th c hi n tr ch xu t d li u b ng l nh Data Advanced k t qu th c hi n cho ph p xu t ra u? A. Sheet1 B. Sheet2 C. Sheet1 ho c Sheet2 u c D. K t qu xu t ra Sheet m i (kh ng ph i Sheet1 hay Sheet2) Answer: B 44. MS Excel 2013: Trong b ng t nh Excel c 2 Sheets (Sheet1 v Sheet2). C s d li u n m tr n Sheet2, mi n i u ki n n m Sheet1, khi ta Sheet1 v th c hi n tr ch xu t d li u b ng l nh Data Advanced k t qu th c hi n cho ph p xu t ra u? A. Sheet1 B. Sheet2 C. Sheet1 ho c Sheet2 u c D. K t qu xu t ra Sheet m i (kh ng ph i Sheet1 hay Sheet2) Answer: A 45. MS Excel 2013: Trong b ng t nh Excel c 2 Sheets (Sheet1 v Sheet2). C s d li u n m tr n Sheet1, mi n i u ki n n m Sheet2, khi ta Sheet2 v th c hi n tr ch xu t d li u b ng l nh Data Advanced k t qu th c hi n cho ph p xu t ra u? A. Sheet1 B. Sheet2 C. Sheet1 ho c Sheet2 u c D. K t qu xu t ra Sheet m i (kh ng ph i Sheet1 hay Sheet2) Answer: B 91. MS Excel 2013: th c hi n t nh n ng l c n ng cao, ta th c hi n c c b c n o sau y A. V o tab Data trong nh m Sort Filter Ch n Sort B. V o tab Data trong nh m Sort Filter Ch n Filter C. V o tab Data trong nh m Sort Filter Ch n Advanced D. V o tab Data trong nh m Advanced Ch n Filter Answer: C 94. MS Excel 2013: T i a ch A1 A1- ng-23 , A2 B1-S a-13 , A3 C1-Mu i- 12 . i n th ng tin t n h ng ng, S a v mu i v o c c a ch B1,B2 B3 B n ph i s d ng c ng th c n o sau y: A. MID(A1, 4, LEN(A1)-5) B. MID(A1, 4, 5) C. MID(A1, 4, 3) D. LEFT(A1, 4, LEN(A1)-5) Answer: A 95. MS Excel 2013: T i a ch A1 Tu n, Nguy n V n , A2 Trang-Tr n Th , A3 Ph ng, L u Th nh l th ng tin h v t n th sinh. i n th ng tin t n th sinh v o c c a ch B1,B2 B3 B n ph i s d ng c ng th c n o sau y: A. LEFT(A1, 4) B. RIGHT(A1, FIND( , ,A1)-1) C. LEFT(A1, 6) D. LEFT(A1, FIND( , ,A1)-1) Answer: D 109. MS Excel 2013: T i a ch A1 Ph ng, H Tr nh Nh l h v t n th sinh, mu n i u t n th sinh t i a ch B1 v h th sinh t i a ch B2. Ta s d ng c c h m n o sau y? A. B1 s d ng c c h m MID, RIGHT t i B2 s d ng c c h m MID, RIGHT, LEN B. B1 s d ng c c h m MID, LEN t i B2 s d ng c c h m MID, LEFT, LEN C. B1 s d ng c c h m LEFT, LEFT t i B2 s d ng c c h m MID, RIGHT, LEN D. B1 s d ng c c h m LEFT, FIND t i B2 s d ng c c h m MID, FIND, LEN Answer: D 118. MS Excel 2013: C c h m sau y h m n o kh ng ph i l h m x l c s d li u? A. DMIN B. DCOUNT C. DSUM D. SUM Answer: D 119. MS Excel 2013: C c h m sau y h m n o kh ng ph i l h m th ng k i u ki n n gi n? A. SUMIF B. COUNTA C. SUMIFS D. DSUM Answer: B 150. MS Excel 2013: T i a ch A1 A- NG-L1 , A2 B- NG-L2 , A3 C- NG-L1 . m c bao nhi u m t h ng lo i 1. Ta ph i d ng c ng th c n o sau y? A. COUNTIFS(A1:A3, L1 ) B. COUNTIFS(A1:A3, 1 ) C. COUNTIF(A1:A3, 1 ) D. COUNTIF(A1:A3, L1 ) Answer: D 151. MS Excel 2013: T i a ch A1 A-G O-L1 , A2 B- NG-L2 , A3 C- NG-L1 , B1 20, B2 10, B3 35 (c t A l m h ng, c t B l s l ng). t nh t ng s l ng m t h ng ng lo i 1. Ta ph i d ng c ng th c n o sau y? A. SUMIFS(B1:B3, A1:A3, L1 , A1:A3, NG ) B. SUMIFS(B1:B3, A1:A3, L1 , A1:A3, NG ) C. SUMIFS(B1:B3, A1:A3, 1 , A1:A3, NG ) D. SUMIFS(B1:B3, A1:A3, L1 , A1:A3, NG ) Answer: A 152. MS Excel 2013: T i a ch A1 A- NG-L1 , A2 B- NG-L2 , A3 C- NG-L1 . m c bao nhi u m t h ng lo i 1. Ta ph i d ng c ng th c n o sau y? A. COUNTIFS(A1:A3, L1 ) B. COUNTIFS(A1:A3, L1 ) C. COUNTIF(A1:A3, L1 ) D. COUNTIF(A1:A3, 1 ) Answer: A 153. MS Excel 2013: T i a ch A1 A-G O-L1 , A2 B- NG-L2 , A3 C- NG-L1 , B1 20, B2 10, B3 35 (c t A l m h ng, c t B l s l ng). t nh t ng s l ng m t h ng ng lo i 1. Ta ph i d ng h m n o sau y? A. DSUM B. DCOUT C. DMIN D. DMAX Answer: A 1. MS Access 2013: File QLBH.accdb thu c phi n b n n o A. 1997 B. 2003 C. 2007 D. 1998 Answer: A 2. MS Access 2013: Ki u t o b ng n o cho ph p ch n ki u d li u c a tr ng th ng tin A. Table Design B. Table C. Table Wizard D. Query Answer: A 3. MS Access 2013: Ki u t o b ng n o cho ph p kh ng c n ch n ki u d li u A. Table B. Table Design C. Table Wizard D. Table Data Type Answer: A 4. MS Access 2013: D ng Field c a giao di n Query Design th hi n i u g A. T n tr ng c ch n B. T n b ng c ch n C. T n li n k t c ch n D. T n b ng kh ng c ch n Answer: A 5. MS Access 2013: D ng Table c a giao di n Query Design th hi n i u g A. T n b ng c ch n B. T n tr ng c ch n C. T n li n k t c ch n D. T n tr ng kh ng c ch n Answer: A 6. MS Access 2013: D ng Sort c a giao di n Query Design th hi n i u g A. T n tr ng c s p x p B. T n b ng c s p x p C. T n li n k t c s p x p D. T n b ng kh ng c s p x p Answer: A 7. MS Access 2013: D ng Show c a giao di n Query Design th hi n i u g A. T n tr ng c hi n th B. T n b ng c hi n th C. T n li n k t c hi n th D. t n b ng kh ng c hi n th Answer: A 8. MS Access 2013: D ng Criteria c a giao di n Query Design th hi n i u g A. T n tr ng l m i u ki n B. T n b ng l m i u ki n C. T n li n k t l m i u ki n D. T n b ng kh ng l m i u ki n Answer: A 9. MS Access 2013: D ng Or c a giao di n Query Design th hi n i u g A. i u ki n c a tr ng th ng tin B. i u ki n c a b ng C. i u ki n c a li n k t D. Kh ng c i u ki n c a b ng Answer: A 10. MS Access 2013: Ki u d li u n o cho ph p l u t i a 255 k t A. Text B. Memo C. Currency D. Yes No Answer: A 11. MS Access 2013: Ki u d li u n o cho ph p l u tr n 255 k t A. Memo B. Text C. Currency D. Yes No Answer: A 12. MS Access 2013: Ki u d li u n o cho ph p l u s 333 A. Number B. Text C. Currency D. Yes No Answer: A 13. MS Access 2013: Ki u d li u n o cho ph p l u s 2.34 A. Number B. Text C. Currency D. Yes No Answer: A 14. MS Access 2013: Ki u d li u n o cho ph p l u s 100000 A. Number - Byte B. Number - Integer C. Number - Long Integer D. Number - Double Answer: A 15. MS Access 2013: Ki u d li u n o cho ph p l u s 2.34 A. Number - Double B. Number - Integer C. Number - Long Integer D. Number - Byte Answer: A 16. MS Access 2013: Ki u d li u n o cho ph p l u gi tr TRUE A. Yes No B. Memo C. Currency D. Text Answer: A 17. MS Access 2013: Ki u d li u n o cho ph p l u gi tr FALSE A. Yes No B. Memo C. Currency D. Text Answer: A 18. MS Access 2013: Ki u d li u n o cho ph p l u gi tr 10 03 2017 A. Date Time B. Memo C. Number D. Text Answer: A 19. MS Access 2013: Ki u d li u n o cho ph p l u gi tr 10 03 2017 A. Date Time - Short Date B. Date Time - Long Date C. Date Time - Medium Date D. Date Time - General Date Answer: A 20. MS Access 2013: Thu c t nh Field Size c a m t tr ng c m c ch g A. X c nh ki u d li u B. nh d ng d li u C. R ng bu c nh p d li u D. i u ki n nh p d li u Answer: A 21. MS Access 2013: Thu c t nh Format c a m t tr ng c m c ch g A. nh d ng d li u B. X c nh ki u d li u C. R ng bu c nh p d li u D. i u ki n nh p d li u Answer: A 22. MS Access 2013: Thu c t nh Input Mask c a m t tr ng c m c ch g A. R ng bu c nh p d li u B. nh d ng d li u C. X c nh ki u d li u D. i u ki n nh p d li u Answer: A 23. MS Access 2013: Thu c t nh Caption c a m t tr ng c m c ch g A. T o nh n cho t n tr ng d li u B. nh d ng d li u C. R ng bu c nh p d li u D. i u ki n nh p d li u Answer: A 24. MS Access 2013: Thu c t nh Default Value c a m t tr ng c m c ch g A. T o m t gi tr ban u cho tr ng d li u B. nh d ng d li u C. R ng bu c nh p d li u D. i u ki n nh p d li u Answer: A 25. MS Access 2013: Thu c t nh Validation Rule c a m t tr ng c m c ch g A. i u ki n nh p d li u B. nh d ng d li u C. R ng bu c nh p d li u D. X c nh ki u d li u Answer: A 26. MS Access 2013: Thu c t nh Validation Text c a m t tr ng c m c ch g A. L i nh c i u ki n nh p d li u B. L i nh c nh d ng d li u C. L i nh c r ng bu c nh p d li u D. L i nh c x c nh ki u d li u Answer: A 27. MS Access 2013: Thu c t nh Required c a m t tr ng c m c ch g A. Y u c u ph i nh p d li u B. nh d ng d li u C. R ng bu c nh p d li u D. i u ki n nh p d li u Answer: A 28. MS Access 2013: Thu c t nh Allow Zero Length c a m t tr ng c m c ch g A. Kh ng cho ph p tr ng d li u B. nh d ng d li u C. R ng bu c nh p d li u D. i u ki n nh p d li u Answer: A 29. MS Access 2013: Thu c t nh Indexed c a m t tr ng c m c ch g A. Ki m so t vi c tr ng l p d li u B. nh d ng d li u C. R ng bu c nh p d li u D. i u ki n nh p d li u Answer: A 30. MS Access 2013: M nh n o ph t bi u ng v Query A. Ch a c u l nh SQL d ng tr ch l c d li u t Table B. Tr ch l c v sao ch p d li u t Table C. Tr ch l c v chuy n d li u t Table D. D ng xem, s a, x a c c Record c a Table Answer: A 31. MS Access 2013: M nh n o ph t bi u ng v Table A. L u tr d li u do ng i d ng nh p li u B. Tr ch l c v sao ch p d li u t Table C. L p b o c o c ngu n th ng tin t Query D. D ng xem, s a, x a c c Record c a Table Answer: A 32. MS Access 2013: M nh n o ph t bi u ng v Form A. D ng xem, s a, x a c c Record c a Table B. Tr ch l c v sao ch p d li u t Table C. L p b o c o c ngu n th ng tin t Query D. L u tr d li u do ng i d ng nh p li u Answer: A 33. MS Access 2013: M nh n o ph t bi u ng v Report A. L p b o c o c ngu n th ng tin t Query B. Tr ch l c v sao ch p d li u t Table C. L u tr d li u do ng i d ng nh p li u D. D ng xem, s a, x a c c Record c a Table Answer: A 34. MS Access 2013: Query d ng tr ch l c th ng tin l lo i Query n o A. Select B. Update C. Delete D. Make Table Answer: A 35. MS Access 2013: Query d ng c p nh t th ng tin l lo i Query n o A. Update B. Select C. Delete D. Append Answer: A 36. MS Access 2013: Query d ng x a th ng tin l lo i Query n o A. Delete B. Update C. Select D. Append Answer: A 37. MS Access 2013: Query cho ph p ng i d ng nh p i u ki n tr ch l c l lo i Query n o A. Select - Parameter B. Select - Criteria C. Select - Total D. Select - Crosstab Answer: A 38. MS Access 2013: Query cho ph p ng i d ng th ng k d li u l lo i Query n o A. Select - Total B. Select - Criteria C. Select - Parameter D. Select - Append Answer: A 39. MS Access 2013: Query cho ph p ng i d ng th ng k d li u l lo i Query n o A. Select - Crosstab B. Select - Criteria C. Select - Parameter D. Select - Append Answer: A 40. MS Access 2013: Query n o cho ph p th ng k t ng th nh ti n c a t ng kh ch h ng A. Select - Total B. Select - Crosstab C. Select - Criteria D. Select - Crosstab Answer: A