.question-container {
  background: white;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  max-width: 800px;
  margin: 0 auto;
}

.question-container.animating {
  opacity: 0.7;
  transform: scale(0.98);
}

.question-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  padding-bottom: 15px;
  border-bottom: 2px solid #e9ecef;
}

.question-info {
  flex: 1;
}

.question-number {
  font-size: 1.1rem;
  font-weight: 600;
  color: #495057;
  margin-bottom: 10px;
  display: block;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #e9ecef;
  border-radius: 4px;
  overflow: hidden;
  max-width: 300px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #007bff, #0056b3);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.result-indicator {
  margin-left: 20px;
}

.correct-badge {
  background: #d4edda;
  color: #155724;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
  border: 1px solid #c3e6cb;
}

.incorrect-badge {
  background: #f8d7da;
  color: #721c24;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
  border: 1px solid #f5c6cb;
}

.question-content {
  margin-bottom: 30px;
}

.question-text {
  margin-bottom: 25px;
}

.question-text h3 {
  margin: 0;
  font-size: 1.3rem;
  line-height: 1.5;
  color: #2c3e50;
  font-weight: 600;
}

.options-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.option {
  display: flex;
  align-items: center;
  padding: 15px;
  border: 2px solid #e9ecef;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
  position: relative;
}

.option:hover {
  border-color: #007bff;
  background: #f8f9fa;
  transform: translateX(5px);
}

.option.selected {
  border-color: #007bff;
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.2);
}

.option.correct {
  border-color: #28a745;
  background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
  box-shadow: 0 2px 8px rgba(40, 167, 69, 0.2);
}

.option.incorrect {
  border-color: #dc3545;
  background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
  box-shadow: 0 2px 8px rgba(220, 53, 69, 0.2);
}

.option-marker {
  flex: 0 0 40px;
  height: 40px;
  border-radius: 50%;
  background: #6c757d;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  transition: all 0.3s ease;
}

.option.selected .option-marker {
  background: #007bff;
}

.option.correct .option-marker {
  background: #28a745;
}

.option.incorrect .option-marker {
  background: #dc3545;
}

.option-letter {
  color: white;
  font-weight: bold;
  font-size: 1.1rem;
}

.option-text {
  flex: 1;
  font-size: 1rem;
  line-height: 1.4;
  color: #495057;
}

.correct-icon {
  flex: 0 0 30px;
  height: 30px;
  background: #28a745;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  margin-left: 10px;
}

.answer-explanation {
  margin-top: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #007bff;
}

.answer-explanation p {
  margin: 5px 0;
  font-size: 0.95rem;
  color: #495057;
}

.question-navigation-buttons {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  padding: 15px 0;
  border-top: 1px solid #e9ecef;
}

.nav-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 120px;
}

.prev-btn {
  background: #6c757d;
  color: white;
}

.prev-btn:hover:not(:disabled) {
  background: #5a6268;
  transform: translateX(-2px);
}

.next-btn {
  background: #007bff;
  color: white;
}

.next-btn:hover:not(:disabled) {
  background: #0056b3;
  transform: translateX(2px);
}

.nav-btn:disabled {
  background: #e9ecef;
  color: #6c757d;
  cursor: not-allowed;
  transform: none;
}

.question-status {
  font-size: 0.9rem;
  font-weight: 600;
  color: #495057;
}

.question-navigation {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 10px;
  border: 1px solid #e9ecef;
}

.question-navigation h4 {
  margin: 0 0 15px 0;
  color: #495057;
  font-size: 1rem;
}

.question-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(45px, 1fr));
  gap: 8px;
  max-height: 200px;
  overflow-y: auto;
}

.nav-question {
  width: 45px;
  height: 45px;
  border: 2px solid #e9ecef;
  background: white;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #495057;
}

.nav-question:hover:not(:disabled) {
  border-color: #007bff;
  background: #f8f9fa;
}

.nav-question.current {
  background: #007bff;
  color: white;
  border-color: #007bff;
}

.nav-question.answered {
  background: #28a745;
  color: white;
  border-color: #28a745;
}

.nav-question.answered.current {
  background: #155724;
  border-color: #155724;
}

.nav-question:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

.question-error {
  text-align: center;
  padding: 40px;
  color: #dc3545;
  font-size: 1.1rem;
}

/* Responsive design */
@media (max-width: 768px) {
  .question-container {
    padding: 20px;
    margin: 10px;
  }
  
  .question-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }
  
  .result-indicator {
    margin-left: 0;
  }
  
  .question-text h3 {
    font-size: 1.2rem;
  }
  
  .option {
    padding: 12px;
  }
  
  .option-marker {
    flex: 0 0 35px;
    height: 35px;
    margin-right: 12px;
  }
  
  .question-navigation-buttons {
    flex-direction: column;
    gap: 15px;
  }
  
  .nav-btn {
    width: 100%;
    min-width: auto;
  }
  
  .question-grid {
    grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
  }
  
  .nav-question {
    width: 40px;
    height: 40px;
  }
}

@media (max-width: 480px) {
  .question-container {
    padding: 15px;
  }
  
  .option {
    padding: 10px;
  }
  
  .option-text {
    font-size: 0.95rem;
  }
  
  .question-navigation {
    padding: 15px;
  }
}
