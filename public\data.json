{"exams": [{"id": "đề_thi_nâng_cao_part_1", "name": "<PERSON><PERSON> thi nâng cao", "description": "<PERSON><PERSON> thi nâng cao - Phần 1 (14 câu)", "duration": 30, "totalQuestions": 14, "questions": [{"id": 1, "question": "MS Winword 2013: <PERSON><PERSON><PERSON> cách nào để thêm một trích dẫn vào văn bản bằng \"References\" trong Microsoft Word?", "options": {"A": "<PERSON>ọn vị trí trong văn bản mà bạn muốn thêm trích dẫn.", "B": "Trong tab \"References,\" chọn \"Insert Citation.\"", "C": "<PERSON><PERSON><PERSON> kiểu trích dẫn phù hợp (ví dụ: APA, MLA) hoặc tạo kiểu trích dẫn tùy chỉnh.", "D": "<PERSON><PERSON><PERSON><PERSON> thông tin của nguồn tham k<PERSON> (tác <PERSON>, tiêu đề, năm xu<PERSON> bả<PERSON>, vv.) trong hộp thoại trích dẫn."}, "correctAnswer": "B", "userAnswer": null, "isAnswered": false}, {"id": 2, "question": "MS Winword 2013: Chủ đề định dạng nào sau đây dùng để làm nghiêng từ", "options": {"A": "Font", "B": "Indent", "C": "Numberring", "D": "Column"}, "correctAnswer": "A", "userAnswer": null, "isAnswered": false}, {"id": 3, "question": "MS Winword 2013: <PERSON><PERSON> thuật nào đặt bức hình lọt giữa văn bản", "options": {"A": "Through", "B": "In Front of Text", "C": "Tight", "D": "Paragraph - Font"}, "correctAnswer": "A", "userAnswer": null, "isAnswered": false}, {"id": 4, "question": "MS Excel 2013: <PERSON><PERSON> bảng t<PERSON>h <PERSON>cel, khi ta thực hiện lệnh Data\\Filter và chỉ định điều kiện lọc tại hai Trườ<PERSON> (cột) trên cơ sở dữ liệu, <PERSON><PERSON><PERSON> biểu nào sau đây là đúng?", "options": {"A": "<PERSON>ơ sở dữ liệu chỉ hiển thị những dòng thỏa mãn cả hai điều kiện lọc tại các cột được chỉ định điều kiện lọc", "B": "<PERSON>ơ sở dữ liệu chỉ hiển thị những dòng thỏa mãn một trong hai điều kiện lọc tại các cột được chỉ định điều kiện lọc", "C": "<PERSON>ơ sở dữ liệu sẽ xuất những dòng thỏa mãn cả hai điều kiện lọc tại các cột được chỉ định điều kiện lọc ra vị trí khác trên bảng tính", "D": "Cơ sở dữ liệu sẽ xuất những dòng không thỏa mãn cả hai điều kiện lọc tại các cột được chỉ định điều kiện lọc ra vị trí khác trên bảng tính"}, "correctAnswer": "A", "userAnswer": null, "isAnswered": false}, {"id": 5, "question": "MS Excel 2013: Tại địa chỉ A1=”A1-Đ<PERSON>ờng-23”, A2=”B1-Sữa-13”, A3=”C1-Muối- 12”. <PERSON><PERSON> điền thông tin tên hàng <PERSON>, <PERSON><PERSON>a và muối vào các địa chỉ B1,B2 B3 B<PERSON>n phải sử dụng công thức nào sau đây:", "options": {"A": "MID(A1, 4, LEN(A1)-5)", "B": "MID(A1, 4, 5)", "C": "MID(A1, 4, 3)", "D": "LEFT(A1, 4, LEN(A1)-5)"}, "correctAnswer": "A", "userAnswer": null, "isAnswered": false}, {"id": 6, "question": "MS Excel 2013: T<PERSON><PERSON> địa chỉ A1=<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, A2=”Trang-Trần Thị”, A3=<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>” là thông tin họ và tên thí sinh. <PERSON><PERSON> điền thông tin tên thí sinh vào các địa chỉ B1,B2 B3 Bạn phải sử dụng công thức nào sau đây:", "options": {"A": "LEFT(A1, 4)", "B": "RIGHT(A1, FIND(“,”,A1)-1)", "C": "LEFT(A1, 6)", "D": "LEFT(A1, FIND(“,”,A1)-1)"}, "correctAnswer": "D", "userAnswer": null, "isAnswered": false}, {"id": 7, "question": "MS Excel 2013: <PERSON><PERSON><PERSON> hàm sau đây hàm nào không phải là hàm xử lý cơ sở dữ liệu?", "options": {"A": "DMIN", "B": "DCOUNT", "C": "DSUM", "D": "SUM"}, "correctAnswer": "D", "userAnswer": null, "isAnswered": false}, {"id": 8, "question": "MS Excel 2013: <PERSON><PERSON><PERSON> hàm sau đây hàm nào không phải là hàm thống kê điều kiện đơn giản?", "options": {"A": "SUMIF", "B": "COUNTA", "C": "SUMIFS", "D": "DSUM"}, "correctAnswer": "B", "userAnswer": null, "isAnswered": false}, {"id": 9, "question": "Ta phải dụng công thức nào sau đây?", "options": {"A": "COUNTIFS(A1:A3,”L1”)", "B": "COUNTIFS(A1:A3,”1”)", "C": "COUNTIF(A1:A3,”1”)", "D": "COUNTIF(A1:A3,”*L1”)"}, "correctAnswer": "D", "userAnswer": null, "isAnswered": false}, {"id": 10, "question": "Ta phải dụng công thức nào sau đây?", "options": {"A": "SUMIFS(B1:B3, A1:A3,”*L1*”, A1:A3,”*ĐƯỜNG*”)", "B": "SUMIFS(B1:B3, A1:A3,”L1”, A1:A3,”ĐƯỜNG”)", "C": "SUMIFS(B1:B3, A1:A3,”*1*”, A1:A3,”ĐƯỜNG”)", "D": "SUMIFS(B1:B3, A1:A3,”L1”, A1:A3,”*ĐƯỜNG*”)"}, "correctAnswer": "A", "userAnswer": null, "isAnswered": false}, {"id": 11, "question": "Ta phải dụng công thức nào sau đây?", "options": {"A": "COUNTIFS(A1:A3,”*L1*”)", "B": "COUNTIFS(A1:A3,”L1”)", "C": "COUNTIF(A1:A3,”L1”)", "D": "COUNTIF(A1:A3,”1”)"}, "correctAnswer": "A", "userAnswer": null, "isAnswered": false}, {"id": 12, "question": "Ta phải dụng hàm nào sau đây?", "options": {"A": "DSUM", "B": "DCOUT", "C": "DMIN", "D": "DMAX"}, "correctAnswer": "A", "userAnswer": null, "isAnswered": false}, {"id": 13, "question": "MS Access 2013: File QLBH.accdb thu<PERSON><PERSON> phiên bản n<PERSON>o", "options": {"A": "1997", "B": "2003", "C": "2007", "D": "1998"}, "correctAnswer": "A", "userAnswer": null, "isAnswered": false}, {"id": 14, "question": "MS Access 2013: <PERSON><PERSON><PERSON> tạo bảng nào cho phép không cần chọn kiểu dữ liệu", "options": {"A": "Table", "B": "Table Design", "C": "Table Wizard", "D": "Table Data Type"}, "correctAnswer": "A", "userAnswer": null, "isAnswered": false}]}]}