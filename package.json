{"name": "be-mang-khoc-nhe", "version": "0.1.0", "private": true, "dependencies": {"@testing-library/dom": "^10.4.1", "@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "fs-extra": "^11.3.0", "pdf-parse": "^1.1.1", "react": "^19.1.1", "react-dom": "^19.1.1", "react-scripts": "5.0.1", "web-vitals": "^2.1.4", "yargs": "^18.0.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "pdf-to-json": "node scripts/pdf-to-json.js", "convert-pdf": "node scripts/pdf-to-json.js"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}