.exam-result-container {
  max-width: 900px;
  margin: 0 auto;
  padding: 20px;
  background: #f8f9fa;
  min-height: 100vh;
}

.result-header {
  text-align: center;
  margin-bottom: 30px;
  background: white;
  padding: 25px;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.result-header h1 {
  margin: 0 0 10px 0;
  color: #2c3e50;
  font-size: 2rem;
  font-weight: 700;
}

.exam-name {
  margin: 0 0 20px 0;
  color: #6c757d;
  font-size: 1.1rem;
}

.view-toggle {
  display: flex;
  gap: 10px;
  justify-content: center;
}

.toggle-btn {
  padding: 10px 20px;
  border: 2px solid #e9ecef;
  background: white;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.95rem;
  font-weight: 600;
}

.toggle-btn:hover {
  border-color: #007bff;
  background: #f8f9fa;
}

.toggle-btn.active {
  background: #007bff;
  color: white;
  border-color: #007bff;
}

.result-content {
  background: white;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  margin-bottom: 25px;
}

/* Summary View Styles */
.result-summary {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.score-display {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 30px;
  padding: 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12px;
}

.score-circle {
  width: 120px;
  height: 120px;
  border: 6px solid;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.score-number {
  font-size: 2rem;
  font-weight: bold;
  line-height: 1;
}

.score-grade {
  font-size: 1.2rem;
  font-weight: 600;
  margin-top: 5px;
}

.score-description h3 {
  margin: 0 0 10px 0;
  font-size: 1.5rem;
}

.score-description p {
  margin: 0;
  color: #6c757d;
  font-size: 1rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 20px;
}

.stat-item {
  text-align: center;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stat-item.correct {
  background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
  border: 1px solid #c3e6cb;
}

.stat-item.incorrect {
  background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
  border: 1px solid #f5c6cb;
}

.stat-item.unanswered {
  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
  border: 1px solid #ffeaa7;
}

.stat-item.time {
  background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
  border: 1px solid #bee5eb;
}

.stat-number {
  font-size: 2rem;
  font-weight: bold;
  color: #2c3e50;
  display: block;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 0.9rem;
  color: #495057;
  font-weight: 600;
}

.result-chart {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 10px;
  border: 1px solid #e9ecef;
}

.chart-bar {
  height: 20px;
  border-radius: 10px;
  overflow: hidden;
  display: flex;
  margin-bottom: 15px;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.chart-segment {
  height: 100%;
  transition: width 0.5s ease;
}

.chart-segment.correct {
  background: linear-gradient(90deg, #28a745, #20c997);
}

.chart-segment.incorrect {
  background: linear-gradient(90deg, #dc3545, #e74c3c);
}

.chart-segment.unanswered {
  background: linear-gradient(90deg, #ffc107, #f39c12);
}

.chart-labels {
  display: flex;
  justify-content: center;
  gap: 20px;
  flex-wrap: wrap;
}

.chart-label {
  font-size: 0.9rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.chart-label::before {
  content: '';
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

.chart-label.correct::before {
  background: #28a745;
}

.chart-label.incorrect::before {
  background: #dc3545;
}

.chart-label.unanswered::before {
  background: #ffc107;
}

/* Detailed View Styles */
.result-detailed {
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.detailed-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.3rem;
  text-align: center;
  padding-bottom: 15px;
  border-bottom: 2px solid #e9ecef;
}

.questions-list {
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.question-result-item {
  border: 1px solid #e9ecef;
  border-radius: 10px;
  overflow: hidden;
}

/* Pagination Styles */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  margin-top: 25px;
  padding: 20px 0;
}

.pagination-btn {
  padding: 8px 16px;
  border: 1px solid #e9ecef;
  background: white;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.pagination-btn:hover:not(:disabled) {
  background: #f8f9fa;
  border-color: #007bff;
}

.pagination-btn:disabled {
  background: #f8f9fa;
  color: #6c757d;
  cursor: not-allowed;
}

.pagination-numbers {
  display: flex;
  gap: 5px;
}

.pagination-number {
  width: 40px;
  height: 40px;
  border: 1px solid #e9ecef;
  background: white;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
  font-weight: 600;
}

.pagination-number:hover {
  background: #f8f9fa;
  border-color: #007bff;
}

.pagination-number.active {
  background: #007bff;
  color: white;
  border-color: #007bff;
}

/* Action Buttons */
.result-actions {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-top: 20px;
}

.action-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 150px;
}

.action-btn.primary {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  color: white;
}

.action-btn.primary:hover {
  background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
  transform: translateY(-1px);
}

.action-btn.secondary {
  background: #6c757d;
  color: white;
}

.action-btn.secondary:hover {
  background: #5a6268;
  transform: translateY(-1px);
}

.result-error {
  text-align: center;
  padding: 40px;
  color: #dc3545;
  font-size: 1.1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .exam-result-container {
    padding: 15px;
  }
  
  .result-header {
    padding: 20px;
  }
  
  .result-header h1 {
    font-size: 1.7rem;
  }
  
  .score-display {
    flex-direction: column;
    gap: 20px;
  }
  
  .score-circle {
    width: 100px;
    height: 100px;
  }
  
  .score-number {
    font-size: 1.7rem;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
  }
  
  .stat-item {
    padding: 15px;
  }
  
  .stat-number {
    font-size: 1.5rem;
  }
  
  .result-actions {
    flex-direction: column;
  }
  
  .action-btn {
    width: 100%;
  }
  
  .pagination {
    flex-wrap: wrap;
    gap: 8px;
  }
  
  .pagination-number {
    width: 35px;
    height: 35px;
  }
}

@media (max-width: 480px) {
  .exam-result-container {
    padding: 10px;
  }
  
  .result-content {
    padding: 20px;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .chart-labels {
    flex-direction: column;
    align-items: center;
    gap: 10px;
  }
  
  .view-toggle {
    flex-direction: column;
    gap: 8px;
  }
  
  .toggle-btn {
    width: 100%;
  }
}
